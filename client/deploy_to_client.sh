#!/bin/bash

# 客户端部署脚本
# 用于将打包后的应用程序部署到远程RK3588客户端

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    print_info "检查必要的工具..."

    if ! command -v sshpass &> /dev/null; then
        print_error "sshpass 未安装，正在安装..."
        sudo apt-get update && sudo apt-get install -y sshpass
    fi

    if ! command -v rsync &> /dev/null; then
        print_error "rsync 未安装，正在安装..."
        sudo apt-get update && sudo apt-get install -y rsync
    fi

    print_success "依赖检查完成"
}

# 获取用户输入
get_user_input() {
    print_info "请输入目标客户端信息："

    read -p "用户名 [默认: orangepi]: " TARGET_USER
    TARGET_USER=${TARGET_USER:-orangepi}

    read -p "IP地址 [默认: **************]: " TARGET_IP
    TARGET_IP=${TARGET_IP:-**************}

    read -s -p "密码 [默认: orangepi]: " TARGET_PASSWORD
    TARGET_PASSWORD=${TARGET_PASSWORD:-orangepi}
    echo

    read -p "目标部署目录 [默认: /home/<USER>/param_set_client]: " TARGET_DIR
    TARGET_DIR=${TARGET_DIR:-/home/<USER>/param_set_client}

    print_info "目标信息："
    print_info "  用户: ${TARGET_USER}"
    print_info "  IP: ${TARGET_IP}"
    print_info "  目录: ${TARGET_DIR}"
}

# 测试SSH连接
test_connection() {
    print_info "测试SSH连接..."

    if sshpass -p "${TARGET_PASSWORD}" ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "echo 'SSH连接成功'" &> /dev/null; then
        print_success "SSH连接测试成功"
    else
        print_error "SSH连接失败，请检查IP地址、用户名和密码"
        exit 1
    fi
}

# 检查本地部署文件
check_local_files() {
    print_info "检查本地部署文件..."

    LOCAL_DEPLOYMENT_DIR="./client/deployment"

    if [ ! -d "${LOCAL_DEPLOYMENT_DIR}" ]; then
        print_error "本地部署目录不存在: ${LOCAL_DEPLOYMENT_DIR}"
        exit 1
    fi

    # 检查关键文件
    REQUIRED_FILES=("rk3588_client" "start.sh" "parameter_mappings.json" ".env")
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "${LOCAL_DEPLOYMENT_DIR}/${file}" ]; then
            print_error "缺少必要文件: ${file}"
            exit 1
        fi
    done

    print_success "本地文件检查完成"
}

# 创建远程目录
create_remote_directory() {
    print_info "创建远程目录..."

    sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "
        mkdir -p ${TARGET_DIR}
        mkdir -p ${TARGET_DIR}/logs
        mkdir -p ${TARGET_DIR}/static
    "

    print_success "远程目录创建完成"
}

# 复制文件到远程
copy_files() {
    print_info "检查文件变化并进行增量传输..."

    LOCAL_DEPLOYMENT_DIR="./client/deployment"

    # 使用rsync进行增量传输，只传输有变化的文件
    # --checksum: 使用校验和而不是时间戳来判断文件是否变化
    # --delete: 删除目标目录中源目录没有的文件
    # --exclude: 排除不需要同步的文件
    print_info "使用rsync进行增量传输..."

    # 先运行dry-run来检查需要传输的文件
    print_info "检查需要传输的文件..."
    RSYNC_OUTPUT=$(sshpass -p "${TARGET_PASSWORD}" rsync -avz --checksum --dry-run \
        --exclude='logs/client.log*' \
        --exclude='.git*' \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        -e "ssh -o StrictHostKeyChecking=no" \
        "${LOCAL_DEPLOYMENT_DIR}/" \
        "${TARGET_USER}@${TARGET_IP}:${TARGET_DIR}/" 2>&1)

    # 分析需要传输的文件数量
    TRANSFERRED_FILES=$(echo "$RSYNC_OUTPUT" | grep -v "^sent\|^total\|^$\|^receiving\|^deleting\|/$\|^sending incremental file list\|^\.\/$" | grep -E "\.(py|html|js|css|json|sh|env)$|^[^./]*$" | wc -l)

    if [ "$TRANSFERRED_FILES" -gt 0 ]; then
        print_info "发现 $TRANSFERRED_FILES 个文件需要更新，开始传输..."

        # 使用rsync进行实际传输，显示详细进度
        # --progress: 显示传输进度
        # --stats: 显示传输统计信息
        # --human-readable: 以人类可读的格式显示大小
        sshpass -p "${TARGET_PASSWORD}" rsync -avz --checksum --progress --stats --human-readable \
            --exclude='logs/client.log*' \
            --exclude='.git*' \
            --exclude='__pycache__' \
            --exclude='*.pyc' \
            -e "ssh -o StrictHostKeyChecking=no" \
            "${LOCAL_DEPLOYMENT_DIR}/" \
            "${TARGET_USER}@${TARGET_IP}:${TARGET_DIR}/"

        # 获取rsync的退出状态
        RSYNC_EXIT_CODE=$?

        # 检查rsync是否成功
        if [ $RSYNC_EXIT_CODE -ne 0 ]; then
            print_error "文件传输失败"
            exit 1
        fi

        export FILES_UPDATED="true"
        print_success "增量传输完成，更新了 $TRANSFERRED_FILES 个文件"

        # 显示传输的文件列表（最多显示10个）
        CHANGED_FILES=$(echo "$RSYNC_OUTPUT" | grep -v "^sent\|^total\|^$\|^receiving\|^deleting\|/$\|^sending incremental file list\|^\.\/$" | grep -E "\.(py|html|js|css|json|sh|env)$|^[^./]*$" | head -10)
        if [ ! -z "$CHANGED_FILES" ]; then
            print_info "更新的文件:"
            echo "$CHANGED_FILES" | while read file; do
                if [ ! -z "$file" ]; then
                    echo "  • $file"
                fi
            done
        fi
    else
        export FILES_UPDATED="false"
        print_info "所有文件都是最新的，无需传输"
    fi

    # 设置全局变量供后续函数使用
    export TRANSFERRED_FILES

    print_success "文件同步完成"
}

# 设置远程文件权限
set_permissions() {
    print_info "设置文件权限..."

    sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "
        chmod +x ${TARGET_DIR}/rk3588_client
        chmod +x ${TARGET_DIR}/start.sh
        chmod 644 ${TARGET_DIR}/parameter_mappings.json
        chmod -R 644 ${TARGET_DIR}/static/*
    "

    print_success "权限设置完成"
}

# 检查环境配置文件
check_env_file() {
    print_info "检查环境配置文件..."

    # .env文件已经在deployment目录中，会随其他文件一起复制
    # 这里只需要验证文件是否存在
    sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "
        if [ -f ${TARGET_DIR}/.env ]; then
            echo '环境配置文件已存在'
        else
            echo '警告: 环境配置文件不存在'
            exit 1
        fi
    "

    print_success "环境配置文件检查完成"
}

# 创建systemd服务文件
create_systemd_service() {
    print_info "创建systemd服务..."

    sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "
        sudo tee /etc/systemd/system/param-set-client.service > /dev/null << 'EOF'
[Unit]
Description=Parameter Set Client Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=${TARGET_USER}
WorkingDirectory=${TARGET_DIR}
ExecStart=${TARGET_DIR}/start.sh
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    "

    print_success "systemd服务文件创建完成"
}

# 启用开机自启动
enable_autostart() {
    print_info "设置开机自启动..."

    sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "
        sudo systemctl daemon-reload
        sudo systemctl enable param-set-client.service
    "

    print_success "开机自启动设置完成"
}

# 启动/重启服务
start_service() {
    # 检查是否有文件更新（通过全局变量传递）
    if [ "$FILES_UPDATED" = "false" ]; then
        print_info "文件无变化，检查服务状态..."

        # 检查服务是否在运行
        SERVICE_RUNNING=$(sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "
            systemctl is-active param-set-client.service 2>/dev/null || echo 'inactive'
        ")

        if [[ "$SERVICE_RUNNING" == "active" ]]; then
            print_success "服务正在正常运行，无需重启"
            return 0
        else
            print_warning "服务未运行，需要启动服务"
        fi
    fi

    print_info "检查服务状态..."

    # 检查服务是否已经在运行
    SERVICE_RUNNING=$(sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "
        systemctl is-active param-set-client.service 2>/dev/null || echo 'inactive'
    ")

    if [[ "$SERVICE_RUNNING" == "active" ]]; then
        if [ "$FILES_UPDATED" = "true" ]; then
            print_info "检测到文件更新，服务需要重启以应用更改"
            read -p "是否立即重启服务以应用更新？(Y/n): " RESTART_NOW
            # 默认为Y，只有明确输入n才不重启
            if [[ ! $RESTART_NOW =~ ^[Nn]$ ]]; then
                print_info "正在重启服务..."
                sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "
                    sudo systemctl restart param-set-client.service
                    sleep 3
                    sudo systemctl status param-set-client.service --no-pager
                "
                print_success "服务重启完成"
            else
                print_info "服务未重启，可以稍后手动重启：sudo systemctl restart param-set-client.service"
            fi
        else
            print_success "服务正在正常运行"
        fi
    else
        print_info "服务未运行，将执行启动操作"
        read -p "是否立即启动服务？(Y/n): " START_NOW
        # 默认为Y，只有明确输入n才不启动
        if [[ ! $START_NOW =~ ^[Nn]$ ]]; then
            print_info "正在启动服务..."
            sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "
                sudo systemctl start param-set-client.service
                sleep 3
                sudo systemctl status param-set-client.service --no-pager
            "
            print_success "服务启动完成"
        else
            print_info "服务未启动，可以稍后手动启动：sudo systemctl start param-set-client.service"
        fi
    fi
}

# 显示部署后的操作说明
show_instructions() {
    print_success "部署完成！"
    echo
    print_info "远程操作命令："
    echo "  启动服务: sudo systemctl start param-set-client.service"
    echo "  停止服务: sudo systemctl stop param-set-client.service"
    echo "  重启服务: sudo systemctl restart param-set-client.service"
    echo "  查看状态: sudo systemctl status param-set-client.service"
    echo "  查看日志: sudo journalctl -u param-set-client.service -f"
    echo "  禁用自启: sudo systemctl disable param-set-client.service"
    echo
    print_info "Web界面访问："
    echo "  主页: http://${TARGET_IP}:7002/"
    echo "  配置页面: http://${TARGET_IP}:7002/config.html"
    echo "  日志管理: http://${TARGET_IP}:7002/log-manager.html"
    echo
    print_info "配置文件位置: ${TARGET_DIR}/.env"
    print_info "日志文件位置: ${TARGET_DIR}/logs/client.log"
    echo
    print_info "当前配置参数："
    sshpass -p "${TARGET_PASSWORD}" ssh -o StrictHostKeyChecking=no "${TARGET_USER}@${TARGET_IP}" "cat ${TARGET_DIR}/.env | grep -v '^#' | grep -v '^$'"
    echo
    print_warning "如需修改配置，请编辑 ${TARGET_DIR}/.env 文件！"
}

# 主函数
main() {
    print_info "开始客户端部署流程..."
    echo

    # 初始化文件更新状态
    FILES_UPDATED="false"

    check_dependencies
    get_user_input
    test_connection
    check_local_files
    create_remote_directory

    # 复制文件（函数内部会设置FILES_UPDATED变量）
    copy_files

    set_permissions
    check_env_file
    create_systemd_service
    enable_autostart
    start_service
    show_instructions

    print_success "部署流程完成！"
}

# 运行主函数
main "$@"
