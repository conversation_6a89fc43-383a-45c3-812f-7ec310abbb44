#!/usr/bin/env python3
"""
ARM64交叉编译脚本 - 使用Docker ARM64容器
"""

import os
import sys
import subprocess
import multiprocessing
from pathlib import Path

def build_arm64_with_docker():
    """使用ARM64 Docker容器进行交叉编译"""

    project_root = Path(__file__).parent

    print("🐳 ARM64 Docker交叉编译工具")
    print("=" * 50)

    # 获取CPU核心数
    cpu_count = multiprocessing.cpu_count()
    jobs = max(1, cpu_count - 1)

    print(f"🚀 使用 {jobs} 个并行任务进行ARM64交叉编译")
    print("📦 正在启动ARM64 Python容器...")

    # Docker运行参数
    docker_cmd = [
        "docker", "run", "--rm",
        "--platform", "linux/arm64",
        "-v", f"{project_root}:/workspace",
        "-w", "/workspace",
        "python:3.10-slim-bullseye",
        "/bin/bash", "-c"
    ]

    # 在容器内执行的编译脚本
    build_script = f"""
set -e

echo "🔧 安装系统依赖..."
apt-get update -qq
apt-get install -y -qq gcc g++ make patchelf

echo "📦 安装Python依赖..."
pip install --quiet --upgrade pip wheel setuptools
pip install --quiet nuitka

echo "📋 安装项目依赖..."
pip install --quiet -r requirements.txt

echo "🏗️  开始ARM64编译..."
python3 -m nuitka \\
    --standalone \\
    --onefile \\
    --output-dir=build \\
    --output-filename=rk3588_client \\
    --jobs={jobs} \\
    --lto=yes \\
    --enable-plugin=anti-bloat \\
    --assume-yes-for-downloads \\
    --static-libpython=no \\
    --show-progress \\
    --include-data-dir=static=static \\
    --include-module=uvicorn \\
    --include-module=fastapi \\
    --include-module=pydantic \\
    --include-module=asyncio \\
    --include-module=socket \\
    --include-module=struct \\
    --include-module=json \\
    --include-module=logging \\
    src/main.py

echo "✅ ARM64编译完成!"
echo "📁 检查编译结果..."
file build/rk3588_client.bin 2>/dev/null || file build/rk3588_client || echo "未找到编译结果"
"""

    # 执行Docker编译
    full_cmd = docker_cmd + [build_script]

    print("⏳ 编译进行中，请耐心等待...")
    print("💡 首次运行需要下载依赖，可能需要较长时间")

    try:
        process = subprocess.run(full_cmd, cwd=project_root, check=True)

        print("✅ ARM64编译成功!")

        # 创建部署包
        create_arm64_deployment_package()

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ ARM64编译失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\\n⚠️  编译被用户中断")
        return False

def create_arm64_deployment_package():
    """创建ARM64部署包"""
    import shutil

    project_root = Path(__file__).parent
    build_dir = project_root / "build"
    deploy_dir = project_root / "deployment"

    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)
    deploy_dir.mkdir()

    print("📦 创建ARM64部署包...")

    # 查找编译结果
    executable_files = []
    for pattern in ["**/rk3588_client", "**/rk3588_client.bin"]:
        executable_files.extend(build_dir.glob(pattern))

    if executable_files:
        executable = executable_files[0]
        dest_file = deploy_dir / "rk3588_client"
        shutil.copy2(executable, dest_file)
        os.chmod(dest_file, 0o755)
        print(f"✅ 复制ARM64可执行文件: {executable.name}")

        # 验证架构
        try:
            result = subprocess.run(["file", str(dest_file)],
                                  capture_output=True, text=True)
            print(f"📋 文件信息: {result.stdout.strip()}")
        except:
            pass
    else:
        print("❌ 未找到编译结果")
        return False

    # 复制配置文件
    for config_file in [".env", "parameter_mappings.json"]:
        src_file = project_root / config_file
        if src_file.exists():
            shutil.copy2(src_file, deploy_dir / config_file)
            print(f"✅ 复制配置文件: {config_file}")

    # 复制静态文件
    static_src = project_root / "static"
    if static_src.exists():
        static_dst = deploy_dir / "static"
        shutil.copytree(static_src, static_dst)
        print("✅ 复制静态文件")

    # 创建启动脚本
    start_script = deploy_dir / "start.sh"
    with open(start_script, 'w') as f:
        f.write("""#!/bin/bash
# RK3588客户端启动脚本 (ARM64版本)

echo "启动RK3588客户端 (ARM64)..."

# 检查架构
ARCH=$(uname -m)
if [ "$ARCH" != "aarch64" ]; then
    echo "警告: 当前架构是 $ARCH，期望的是 aarch64"
fi

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "错误: 未找到配置文件 .env"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动客户端
./rk3588_client

echo "客户端已停止"
""")
    os.chmod(start_script, 0o755)
    print("✅ 创建启动脚本")

    # 创建日志目录
    (deploy_dir / "logs").mkdir(exist_ok=True)

    print(f"✅ ARM64部署包已创建: {deploy_dir}")
    return True

if __name__ == "__main__":
    success = build_arm64_with_docker()

    print("=" * 50)
    if success:
        print("🎉 ARM64交叉编译完成!")
        print("📁 部署包位置: ./deployment/")
        print("🚀 可以直接部署到RK3588设备")
        print("💡 使用 ./deploy_to_client.sh 进行部署")
    else:
        print("❌ ARM64交叉编译失败")
        sys.exit(1)
